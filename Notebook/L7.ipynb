{"cells": [{"cell_type": "markdown", "id": "5230b383", "metadata": {}, "source": ["<style>\n", "/* 清除 body 的 margin，防止被伪元素影响布局 */\n", "body {\n", "    margin: 0;\n", "}\n", "\n", "/* 完全清除 body::before 任何默认行为 */\n", "body::before {\n", "    all: unset;\n", "    content: \"\";\n", "    background: url('https://i.postimg.cc/s2ZMPzBM/temp-Image9i-KMM0.avif') no-repeat;\n", "    background-size: contain;\n", "    position: fixed;\n", "    top: 32px;\n", "    right: 32px;\n", "    width: 256px;\n", "    height: 77px;\n", "    z-index: 999999;\n", "    pointer-events: none;\n", "    opacity: 0.9;\n", "    display: block;\n", "}\n", "\n", "/* 控制 slide 中图片最大高度 */\n", ".reveal .slides img {\n", "    max-height: 420px;\n", "    width: auto;\n", "}\n", "\n", "/* 顶部进度条 */\n", ".reveal .progress {\n", "    position: absolute;\n", "    top: 0;\n", "    left: 0;\n", "    bottom: auto;\n", "    width: 100%;\n", "    height: 5px;\n", "    background: #4A8FD3;\n", "    z-index: 9999;\n", "}\n", "\n", ".reveal .progress span {\n", "    display: block;\n", "    height: 5px;\n", "    transition: width 0.2s ease;\n", "    background: rgb(255, 227, 113);\n", "}\n", "\n", "/* 给 SVG 箭头图标染色（用 filter 实现） */\n", ".reveal .navigate-left,\n", ".reveal .navigate-right,\n", ".reveal .navigate-up,\n", ".reveal .navigate-down {\n", "    filter: invert(23%) sepia(100%) saturate(1533%) hue-rotate(198deg) brightness(90%) contrast(89%);\n", "}\n", "\n", "</style>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# 探索HTTP协议与FastAPI入门：构建你自己的API"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["欢迎来到网络通信与API开发的奇妙世界！在这个课程中，我们将深入了解HTTP协议如何使互联网世界保持连接，并学习如何使用Python的FastAPI框架，像真正的工程师一样，创建我们自己的API！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 深入HTTP：网络世界的“通用语”\n", "\n", "我们知道IP地址是“地址”，那电脑之间用什么“语言”交流呢？它们使用的就是一套共同的规则和标准——**网络协议**。其中，我们上网时最常用到的就是**HTTP协议**。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### HTTP请求报文：一张详细的“订单”\n", "当你的浏览器访问一个网站时，它会发送一个HTTP请求。这就像一张详细的订单，告诉服务器你想要什么。我们以访问百度搜索“python”为例，看看这张“订单”长什么样：\n", "```http\n", "GET /s?q=python HTTP/1.1\n", "Host: www.baidu.com\n", "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) ...\n", "Accept: text/html,application/xhtml+xml,...\n", "```\n", "这张“订单”包含了几个关键部分。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### 订单解读 (1): 请求行 (Request Line)\n", "`GET /s?q=python HTTP/1.1`\n", "- `GET`: **请求方法**。告诉服务器我想“获取(Get)”数据。\n", "- `/s?q=python`: **请求路径(Path)和查询参数(Query)**。告诉服务器我想访问`/s`这个功能，并且查询的关键词是`python`。\n", "- `HTTP/1.1`: **协议版本**。告诉服务器我们用的是1.1版本的HTTP协议。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### 订单解读 (2): 请求头 (Headers)\n", "请求头是关于这次请求的补充说明，它们非常重要！\n", "- `Host: www.baidu.com`\n", "  - **这是什么？** 目标服务器的域名。 \n", "  - **为什么要写？** 因为一台物理服务器可能托管着很多个网站（比如`www.a.com`和`www.b.com`）。`Host`头告诉服务器：“我是来找`www.baidu.com`的，不是找别人的！” **在HTTP/1.1协议中，这是必须的！不写，服务器就不知道你要访问哪个网站，会报错。**\n", "\n", "- `User-Agent: Mozilla/5.0 ...`\n", "  - **这是什么？** 你的“身份标识”，告诉服务器你用的是什么浏览器、什么操作系统。\n", "  - **为什么要写？** 有些网站会根据你的设备提供不同的页面（比如手机版和电脑版）。有些API为了防止被恶意程序滥用，会拒绝没有`User-Agent`的请求。我们之前用`requests`库时，它就自动帮我们加了一个默认的`User-Agent`。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## GET 与 POST 的终极对决"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["我们已经知道`GET`用于“拿”数据，`POST`用于“送”数据。它们的根本区别在于**数据如何被携带**。\n", "\n", "### `GET`请求：数据在“大喇叭”里喊\n", "- **数据位置**: 在URL中，所有人都能看到。\n", "  - `.../search?keyword=python`\n", "- **安全性**: 低。绝不能用GET请求发送密码等敏感信息！\n", "- **长度限制**: 因为URL有长度限制，所以不适合发送大量数据。\n", "\n", "### `POST`请求：数据装在“保密信封”里\n", "- **数据位置**: 在一个独立的、不可见的**请求体 (Request Body)** 中。\n", "- **安全性**: 高。因为数据不在URL里，所以更安全。\n", "- **长度限制**: 几乎没有限制，可以发送大段文章、上传文件等。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### `POST` 请求体的不同“格式” (`Content-Type`)\n", "`POST`请求的请求头里通常有一个很重要的字段`Content-Type`，它告诉服务器我们“信封”里的数据是什么格式的。\n", "\n", "- **`application/x-www-form-urlencoded`**: \n", "  - 最常见的表单提交格式。\n", "  - 数据被编码成键值对，像URL参数一样，例如 `username=xiaoming&password=123`。\n", "\n", "- **`application/json`**:\n", "  - 当我们想发送更复杂的结构化数据时使用。\n", "  - 请求体里直接就是一个JSON字符串，例如 `{\"username\": \"xiaoming\", \"age\": 12}`。\n", "\n", "**我们用`requests`库时，`data=`参数对应第一种，`json=`参数对应第二种。**"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 探索HTTP的瑞士军刀：`curl`"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["`curl` 是一个强大的命令行工具，是每个后端工程师的必备神器。它可以用来发送各种复杂的HTTP请求，帮助我们快速测试API。\n", "```bash\n", "# 最简单的GET请求，获取你的公网IP\n", "curl ip.sb\n", "```\n", "```bash\n", "# 一个复杂的POST请求，发送JSON数据\n", "curl -X POST -H \"Content-Type: application/json\" -d '{\"title\":\"test\"}' https://jsonplaceholder.typicode.com/posts\n", "```\n", "- `-X POST`: 指定请求方法为`POST` (X是eXchange的意思)。\n", "- `-H \"...\"`: 添加一个请求头 (Header)，这里我们指定了内容类型是JSON。\n", "- `-d '...'`: 指定请求体的数据 (data)。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 创建你的第一个API：使用FastAPI"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["FastAPI是一个现代、快速的Python Web框架，专为API开发而设计。它非常容易上手，而且功能强大。\n", "\n", "### 准备工作\n", "\n", "1.  **安装必要的库**：\n", "    ```bash\n", "    pip install fast<PERSON>i uvicorn\n", "    ```\n", "    - `fastapi`: 框架本身。\n", "    - `uvicorn`: 一个高性能的Web服务器，用来运行我们的FastAPI应用。\n", "\n", "2.  **创建文件**: 新建一个名为 `main.py` 的Python文件。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 编写API代码\n", "我们将制作两个API端点：\n", "1.  一个`GET`请求，访问根路径 `/` 时，返回一句固定的问候。\n", "2.  另一个`GET`请求，访问 `/greet` 路径并带上`name`参数时，返回一句个性化的问候。\n", "\n", "**请在`main.py`文件中输入以下代码：**"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "# main.py\n", "from fastapi import FastAPI\n", "\n", "# 1. 创建一个FastAPI实例，就像创建了一个应用对象\n", "app = FastAPI()\n", "\n", "# 2. 创建一个GET端点\n", "# @app.get(\"/\") 是一个“装饰器”，它告诉FastAPI：\n", "# “如果有人用GET方法访问根路径'/'，请执行紧跟在下面的这个函数”\n", "@app.get(\"/\")\n", "def read_root():\n", "    return {\"message\": \"你好，世界!\"}\n", "\n", "# 3. 创建另一个带参数的GET端点 (这里纠正PPT中的错误，应为GET)\n", "# 这个装饰器告诉FastAPI，处理对'/greet'路径的GET请求\n", "@app.get(\"/greet\")\n", "# (name: str = \"同学\") 定义了一个名为name的查询参数\n", "# 它的类型是字符串，默认值是“同学”\n", "def greet_user(name: str = \"同学\"):\n", "    return {\"message\": f\"你好, {name}!\"}\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 运行和测试你的API\n", "\n", "1.  **启动服务器**: 在包含`main.py`的文件夹的命令行中，运行以下命令：\n", "    ```bash\n", "    uvicorn main:app --reload\n", "    ```\n", "    - `main`: 指的是`main.py`文件。\n", "    - `app`: 指的是我们在文件中创建的`app = FastAPI()`那个对象。\n", "    - `--reload`: 一个超好用的参数，当你修改并保存`main.py`时，服务器会自动重启！\n", "\n", "2.  **在浏览器中测试**:\n", "    - 访问 `http://127.0.0.1:8000/`\n", "    - 访问 `http://127.0.0.1:8000/greet?name=小明`\n", "\n", "3.  **查看自动生成的API文档**:\n", "    - 访问 `http://127.0.0.1:8000/docs`。你会看到一个漂亮的交互式文档，你甚至可以在那里直接测试你的API！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 练习：与你的第一个API服务器“对话”\n", "\n", "现在，让我们用之前学过的`requests`库，来和我们自己写的API服务器进行“对话”。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["请求 / 的结果: {'message': '你好，世界!'}\n", "请求 /greet 的结果: {'message': '你好, 工程师!'}\n"]}], "source": ["import requests\n", "\n", "# 确保你的FastAPI服务器正在运行！\n", "\n", "# 测试第一个端点: /\n", "try:\n", "    response_root = requests.get(\"http://127.0.0.1:8000/\")\n", "    print(\"请求 / 的结果:\", response_root.json())\n", "except requests.ConnectionError:\n", "    print(\"连接服务器失败，请确保 uvicorn main:app --reload 正在运行！\")\n", "\n", "# 测试第二个端点: /greet\n", "try:\n", "    # 构造带参数的URL\n", "    params = {'name': '工程师'}\n", "    response_greet = requests.get(\"http://127.0.0.1:8000/greet\", params=params)\n", "    print(\"请求 /greet 的结果:\", response_greet.json())\n", "except requests.ConnectionError:\n", "    print(\"连接服务器失败！\")"]}, {"cell_type": "markdown", "id": "e259bb46", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "import requests\n", "\n", "# 确保你的FastAPI服务器正在运行！\n", "\n", "# 测试第一个端点: /\n", "try:\n", "    response_root = requests.get(\"http://127.0.0.1:8000/\")\n", "    print(\"请求 / 的结果:\", response_root.json())\n", "except requests.ConnectionError:\n", "    print(\"连接服务器失败，请确保 uvicorn main:app --reload 正在运行！\")\n", "\n", "# 测试第二个端点: /greet\n", "try:\n", "    # 构造带参数的URL\n", "    params = {'name': '工程师'}\n", "    response_greet = requests.get(\"http://127.0.0.1:8000/greet\", params=params)\n", "    print(\"请求 /greet 的结果:\", response_greet.json())\n", "except requests.ConnectionError:\n", "    print(\"连接服务器失败！\")\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 挑战：实时修改\n", "\n", "1.  不要关闭运行`uvicorn`的命令行窗口。\n", "2.  打开`main.py`文件，将`/greet`函数中的返回消息修改成 `f\"Welcome, {name}!\"`。\n", "3.  **保存文件**。\n", "4.  观察命令行窗口，你会看到服务器自动重启了！\n", "5.  回到IDE，**重新运行**上面的代码。\n", "6.  看看`/greet`端点的返回结果是不是已经变成了新的英文问候？这就是`--reload`的魔力！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 总结\n", "\n", "- 我们深入理解了**HTTP协议**，特别是`GET`和`POST`的本质区别，以及`Host`、`User-Agent`等关键请求头的意义。\n", "- 我们学会了使用命令行神器 **`curl`** 来快速测试网络请求。\n", "- 我们掌握了使用 **FastAPI** 框架来创建、运行和测试我们自己的API服务。\n", "- 我们体验了后端开发的完整流程：**编写代码 -> 运行服务 -> 使用客户端(浏览器或requests)测试**。\n", "\n", "恭喜你，你已经从一个API的“使用者”，成功进阶为一名API的“创造者”了！"]}], "metadata": {"kernelspec": {"display_name": "XFrame", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}