{"cells": [{"cell_type": "markdown", "id": "59f566c7", "metadata": {}, "source": ["<style>\n", "/* 清除 body 的 margin，防止被伪元素影响布局 */\n", "body {\n", "    margin: 0;\n", "}\n", "\n", "/* 完全清除 body::before 任何默认行为 */\n", "body::before {\n", "    all: unset;\n", "    content: \"\";\n", "    background: url('https://i.postimg.cc/s2ZMPzBM/temp-Image9i-KMM0.avif') no-repeat;\n", "    background-size: contain;\n", "    position: fixed;\n", "    top: 32px;\n", "    right: 32px;\n", "    width: 256px;\n", "    height: 77px;\n", "    z-index: 999999;\n", "    pointer-events: none;\n", "    opacity: 0.9;\n", "    display: block;\n", "}\n", "\n", "/* 控制 slide 中图片最大高度 */\n", ".reveal .slides img {\n", "    max-height: 420px;\n", "    width: auto;\n", "}\n", "\n", "/* 顶部进度条 */\n", ".reveal .progress {\n", "    position: absolute;\n", "    top: 0;\n", "    left: 0;\n", "    bottom: auto;\n", "    width: 100%;\n", "    height: 5px;\n", "    background: #4A8FD3;\n", "    z-index: 9999;\n", "}\n", "\n", ".reveal .progress span {\n", "    display: block;\n", "    height: 5px;\n", "    transition: width 0.2s ease;\n", "    background: rgb(255, 227, 113);\n", "}\n", "\n", "/* 给 SVG 箭头图标染色（用 filter 实现） */\n", ".reveal .navigate-left,\n", ".reveal .navigate-right,\n", ".reveal .navigate-up,\n", ".reveal .navigate-down {\n", "    filter: invert(23%) sepia(100%) saturate(1533%) hue-rotate(198deg) brightness(90%) contrast(89%);\n", "}\n", "\n", "</style>"]}, {"cell_type": "markdown", "id": "96c75ab5", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# 网络世界大冒险：揭秘IP地址与家庭网络"]}, {"cell_type": "markdown", "id": "03aa9f94", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["同学们好！我们每天都在上网、看视频、玩游戏，但你想过，当你在浏览器输入一个网址时，你的电脑是如何在浩瀚的互联网中，精确找到那台存放着网站的服务器的吗？\n", "\n", "今天，我们将化身网络侦探，一起探索互联网世界的奥秘，了解IP地址、路由器这些“幕后英雄”是如何帮助信息传递的！"]}, {"cell_type": "markdown", "id": "5e6f625c", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 核心概念：网络通信需要地址"]}, {"cell_type": "markdown", "id": "de4925c4", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["想象一下寄快递的场景，你必须在包裹上写清楚收件人的详细地址，快递员才能准确地把包裹送到对方手中。\n", "\n", "网络世界也一样！每一台连接到网络上的设备，无论是你的电脑、手机，还是B站的服务器，都必须有一个**独一无二的地址**，这样数据包（就像网络世界的快递包裹）才能找到正确的目的地。\n", "\n", "这个特殊的数字地址，就叫做 **IP地址 (Internet Protocol Address)**。"]}, {"cell_type": "markdown", "id": "f8d9a345", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 认识我们的“地址”：IP地址的种类"]}, {"cell_type": "markdown", "id": "57fd85c4", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["IP地址主要分为两种，就像我们家里的“房间号”和“家庭住址”一样。\n", "\n", "### 1. 私有IP地址 (Private IP)\n", "- **就像你家里的“房间号”** (例如：主卧、次卧、书房)。\n", "- **例子**: `***********01`\n", "- **特点**: 只在你自己的**局域网（家庭网络）**内有效。你跟你妈妈说“我在书房”，她能听懂。但你跟快递员说“请送到书房”，他肯定找不到。\n", "\n", "### 2. 公共IP地址 (Public IP)\n", "- **就像你家的“完整家庭住址”** (例如：XX市XX区XX街道XX号)。\n", "- **例子**: `***************`\n", "- **特点**: 在整个互联网上是**唯一**的，是你的家庭网络在世界上的身份标识。"]}, {"cell_type": "markdown", "id": "9a93f19e", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 特殊的地址：本地回环地址 (Loopback)\n", "\n", "还有一个非常特殊的IP地址，它不用于和别人通信，而是用来和**自己**通信。\n", "\n", "- **地址**: `127.0.0.1`\n", "- **作用**: 指向设备自身，就像你对自己说话一样。\n", "- **用途**: 程序员经常用它来测试自己电脑上开发的服务是否正常运行，而不需要真的连接到外部网络。"]}, {"cell_type": "markdown", "id": "f6699f92", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 家庭网络的“交通警察”：路由器"]}, {"cell_type": "markdown", "id": "3b039a2f", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["我们的电脑和手机是怎么同时拥有“房间号”（私有IP）和“家庭住址”（公共IP）的呢？这就要归功于我们家里的网络中枢——**路由器**。\n", "\n", "路由器就像一个身兼数职的、聪明的“交通警察”或“大楼管理员”。"]}, {"cell_type": "markdown", "id": "78501517", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 路由器的魔法功能\n", "\n", "1.  **DHCP服务器 (宿管阿姨)**: 当你的手机连上WiFi时，路由器会自动给你分配一个**不重复的**私有IP地址（房间号），并告诉你“大门”（网关）在哪。这个过程叫**动态主机配置协议(DHCP)**。\n", "\n", "2.  **网关 (大楼门口)**: 路由器是你的家庭网络（局域网）通往外部互联网（广域网）的**唯一出口**。所有想“出门”或“进门”的数据包都必须经过它。\n", "\n", "3.  **NAT转换 (前台/收发室)**: 当你的电脑想访问B站时，路由器会把你的请求的源地址从你的私有IP（房间号）“翻译”成你们家唯一的公共IP（家庭住址）再发出去。这个神奇的“地址翻译”技术叫做**网络地址转换(NAT)**。正因为有它，我们家里多台设备才能共用一个公共IP上网。"]}, {"cell_type": "markdown", "id": "ef7d207c", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 如何查看自己的IP地址？"]}, {"cell_type": "markdown", "id": "0ac4a969", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["我们可以使用操作系统自带的命令行工具来查看自己电脑的网络配置。\n", "\n", "- **Windows**: \n", "  1. 按下 `Win` + `R` 键，输入 `cmd` 并回车。\n", "  2. 在弹出的黑色窗口中输入 `ipconfig` 并回车。\n", "\n", "- **Mac / Linux**:\n", "  1. 打开“终端(Terminal)”应用程序。\n", "  2. 输入 `ifconfig` 或 `ip a` 并回车。\n", "\n", "**我们来找找看，你的“房间号”（私有IP，一般叫`IPv4 Address`）和“大门口地址”（默认网关，`Default Gateway`）是多少？**"]}, {"cell_type": "markdown", "id": "768693de", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 动手实验：探索真实世界的网络配置\n", "\n", "**注意：接下来的实验需要登录你家里的路由器管理后台。这可能会影响家庭网络，请务必在家长或老师的指导下进行！**"]}, {"cell_type": "markdown", "id": "0289c0b2", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 实验准备：进入路由器的“驾驶舱”\n", "\n", "1.  **找到网关地址**: 运行 `ipconfig` 或 `ifconfig`，找到“默认网关(Default Gateway)”的地址，它通常是 `***********` 或 `***********` 之类的。\n", "\n", "2.  **登录后台**: \n", "    - 打开浏览器（如Chrome, Edge）。\n", "    - 在地址栏输入你刚刚找到的网关地址，然后回车。\n", "    - 你会看到一个登录页面，要求输入用户名和密码。这个密码通常写在路由器底部的标签上（如果没修改过的话）。"]}, {"cell_type": "markdown", "id": "ef3118cc", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 实验一：DHCP的奥秘——谁在给你分配“门牌号”？"]}, {"cell_type": "markdown", "id": "05b7778f", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### Part 1: 观察“自动分配”\n", "1.  在电脑命令行中用 `ipconfig` / `ifconfig` 查看并记下你当前的 “IPv4 地址”，例如 `*************`。\n", "2.  登录路由器后台，找到名为“DHCP服务器”、“客户端列表”或“已连接设备”的菜单。\n", "3.  **核对信息**: 在列表中，你应该能找到你的电脑名称，以及它对应的IP地址，应该和你刚刚记下的一模一样！\n", "4.  **验证**: 断开你电脑的WiFi再重新连接。再次执行`ipconfig`，你的IP地址可能会改变（也可能不变，因为DHCP有“租期”）。这证明了地址确实是路由器动态分配的。"]}, {"cell_type": "markdown", "id": "b1d67c76", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### Part 2: 预留“专属车位”（IP与MAC绑定）\n", "我们可以告诉路由器，让它永远给我们的电脑分配同一个固定的IP地址。\n", "\n", "1.  **找到MAC地址**: 它是设备的唯一硬件ID，就像身份证号。在`ipconfig /all` (Win) 或 `ifconfig` (Mac) 中找到“物理地址(Physical Address)”或“ether”。\n", "2.  **进入地址保留设置**: 在路由器后台的“DHCP服务器”菜单下，找到“静态地址分配”或“地址保留”。\n", "3.  **创建绑定规则**:\n", "    - 点击“新增”或“添加”。\n", "    - 在“MAC地址”栏，粘贴你电脑的物理地址。\n", "    - 在“IP地址”栏，输入一个你希望它永远使用的地址，例如 `************`。\n", "    - 点击“保存”。\n", "4.  **验证**: 重启电脑的网络连接后，再次 `ipconfig`，你会发现你的IP地址永远变成了你设定的那个！"]}, {"cell_type": "markdown", "id": "6aa29ee0", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 实验二：NAT与端口映射——为你的游戏服务器打开一扇“窗”"]}, {"cell_type": "markdown", "id": "933d09aa", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### Part 1: 在本地“搭建”一个简易Web服务器\n", "Python有一个神奇的内置功能，可以一句话就把任何文件夹变成一个网站服务器！\n", "\n", "1.  在你的电脑上创建一个空文件夹，比如 `MyWebServer`。\n", "2.  打开命令行，**进入(cd)** 到这个文件夹里。\n", "3.  输入命令 `python -m http.server 8888` 并回车。（`8888`是端口号，可以换成其他数字）\n", "4.  **本地验证**: 在你自己的浏览器中访问 `http://127.0.0.1:8888`。如果能看到文件夹内容，说明服务器已成功运行！ **（请保持命令行窗口不要关闭）**"]}, {"cell_type": "markdown", "id": "f59becfb", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### Part 2: 尝试从“外网”访问 (失败乃成功之母)\n", "1.  **获取公网IP**: 在浏览器里访问 `www.ip.cn` 或 `whatismyip.com`，记下你家的公网IP地址。\n", "2.  **模拟外部访问**: 使用你的手机，**关闭WiFi**，改用蜂窝数据网络（确保你是从外网访问）。\n", "3.  在手机浏览器中，输入 `http://<你记下的公网IP>:8888`。\n", "4.  **预期结果**: 页面无法加载，会一直转圈直到超时。这是因为路由器的NAT功能像一堵墙，挡住了来自外部的未知请求。"]}, {"cell_type": "markdown", "id": "0a2c6952", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### Part 3 & 4: 配置端口映射规则并见证奇迹\n", "端口映射就是告诉路由器：“如果有人敲响`8888`号门，请直接把他带到我房间（我的电脑）！”\n", "\n", "1.  登录路由器后台，找到名为“**转发规则**”、“**虚拟服务器**”或“**端口映射**”的菜单。\n", "2.  **创建新规则**:\n", "    - **外部端口 (或服务端口)**: `8888`\n", "    - **内部IP地址**: 输入你电脑的内网IP地址（用`ipconfig`查到的那个）。\n", "    - **内部端口**: `8888` (与外部端口保持一致)\n", "    - **协议**: 选择 `TCP` 或 `ALL`。\n", "    - 点击“保存”。\n", "3.  **再次从外网访问**: 拿起你的手机（确保仍在使用蜂窝数据），刷新或重新访问 `http://<你的公网IP>:8888`。\n", "4.  **预期结果**: 成功！你的手机上现在应该能看到你电脑上 `MyWebServer` 文件夹里的内容了！你成功地为你的服务在路由器上开了一扇“窗”。"]}, {"cell_type": "markdown", "id": "21d84fe6", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 总结\n", "\n", "今天，我们不仅学习了网络世界的理论知识，还亲自动手配置了真实的家庭网络，这非常了不起！\n", "\n", "- 我们理解了**私有IP**和**公网IP**的区别，就像房间号和家庭住址。\n", "- 我们揭开了**路由器**三大魔法功能（DHCP, 网关, NAT）的神秘面纱。\n", "- 我们学会了用 `ipconfig` 查看网络配置，用 `ping` 测试网络连通性。\n", "- 我们通过**端口映射**，成功地把一个自己电脑上的服务“发布”到了互联网上！"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}