{"cells": [{"cell_type": "markdown", "id": "a34b9d70", "metadata": {}, "source": ["<style>\n", "/* 清除 body 的 margin，防止被伪元素影响布局 */\n", "body {\n", "    margin: 0;\n", "}\n", "\n", "/* 完全清除 body::before 任何默认行为 */\n", "body::before {\n", "    all: unset;\n", "    content: \"\";\n", "    background: url('https://i.postimg.cc/s2ZMPzBM/temp-Image9i-KMM0.avif') no-repeat;\n", "    background-size: contain;\n", "    position: fixed;\n", "    top: 32px;\n", "    right: 32px;\n", "    width: 256px;\n", "    height: 77px;\n", "    z-index: 999999;\n", "    pointer-events: none;\n", "    opacity: 0.9;\n", "    display: block;\n", "}\n", "\n", "/* 控制 slide 中图片最大高度 */\n", ".reveal .slides img {\n", "    max-height: 420px;\n", "    width: auto;\n", "}\n", "\n", "/* 顶部进度条 */\n", ".reveal .progress {\n", "    position: absolute;\n", "    top: 0;\n", "    left: 0;\n", "    bottom: auto;\n", "    width: 100%;\n", "    height: 5px;\n", "    background: #4A8FD3;\n", "    z-index: 9999;\n", "}\n", "\n", ".reveal .progress span {\n", "    display: block;\n", "    height: 5px;\n", "    transition: width 0.2s ease;\n", "    background: rgb(255, 227, 113);\n", "}\n", "\n", "/* 给 SVG 箭头图标染色（用 filter 实现） */\n", ".reveal .navigate-left,\n", ".reveal .navigate-right,\n", ".reveal .navigate-up,\n", ".reveal .navigate-down {\n", "    filter: invert(23%) sepia(100%) saturate(1533%) hue-rotate(198deg) brightness(90%) contrast(89%);\n", "}\n", "\n", "</style>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# API网络共享与健壮性开发"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["上节课，我们成功创建了自己的第一个API，但它就像一个很棒的玩具，只能在自己的电脑上访问（通过`127.0.0.1`）。\n", "\n", "今天，我们将学习如何让你的API走出“孤岛”，在整个局域网中分享！同时，我们会进一步探索API开发的实际应用，并学习如何处理可能出现的各种问题，让我们的API变得更加“健壮”！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第一步：让你的API走出“孤岛”"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 什么是监听地址？\n", "当我们运行`uvicorn`时，它默认只监听来自本机的请求。这就像一个只对自己说话的电话，别人听不到。\n", "- **`127.0.0.1` (本机地址/localhost)**: 这是一个特殊的地址，永远指向“我自己这台电脑”。默认情况下，服务只监听这个地址，所以只有你自己能访问。\n", "\n", "为了让局域网里的其他同学也能访问我们的API，我们需要让服务器“把扬声器打开”。\n", "- **`0.0.0.0` (全网监听)**: 这个地址告诉服务器：“请监听**所有**网络接口收到的请求”。这包括来自你本机（127.0.0.1）的，也包括来自局域网其他电脑的。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 修改FastAPI启动命令\n", "我们只需要在启动`uvicorn`时，添加两个参数：`--host`和`--port`。\n", "\n", "```bash\n", "uvicorn main:app --reload --host 0.0.0.0 --port 8000\n", "```\n", "- `--host 0.0.0.0`: **核心！** 让API对整个局域网可见。\n", "- `--port 8000`: 指定一个“门牌号”（端口号）。其他设备需要通过 `IP地址:端口号` 的方式来访问。\n", "- `--reload`: 开发时的好帮手，代码修改后自动重启。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 全班互联！你的第一个网络服务\n", "1.  **找到你的局域网IP**: 用`ipconfig` (<PERSON>) 或 `ifconfig` (Mac) 找到你的“IPv4 Address”，例如 `*************`。\n", "2.  **启动服务**: 运行 `uvicorn main:app --reload --host 0.0.0.0 --port 8000`。\n", "3.  **分享地址**: 告诉你的同桌你的API地址，格式是：`http://<你的IP地址>:<端口号>/`，例如 `http://*************:8000/`。\n", "4.  **互相访问**: 让你的同桌用浏览器或我们之前写的`requests`代码来访问你的API，看看他是否能成功！\n", "\n", "**可能遇到的问题**: 如果无法连接，很可能是电脑的**防火墙**阻止了外部访问。你需要允许Python或指定端口（如8000）的入站连接。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 实践项目：一个能用但“脆弱”的心情记录API"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### API设计\n", "我们将开发一个简单的心情记录API，它有三个功能：\n", "- **`POST /moods`**: 创建一条新的心情记录。需要提供：`user`(用户名) 和 `mood`(心情)。\n", "- **`GET /moods`**: 获取所有人发布的所有心情记录。\n", "- **`GET /moods/{user_name}`**: 查询特定用户的所有心情记录。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### “脆弱”的第一版实现\n", "我们先用最简单的方式来实现`POST /moods`功能。我们用一个列表来当做“数据库”，用一个普通字典来接收用户发来的数据。\n", "\n", "**请在`main.py`中添加以下代码（可以先注释掉之前的代码）：**\n", "```python\n", "from fastapi import FastAPI\n", "app = FastAPI()\n", "# 用一个列表来模拟数据库，存储所有心情记录\n", "mood_items = []\n", "# 用一个全局变量来模拟自增ID\n", "mood_counter = 0\n", "\n", "@app.post(\"/moods\")\n", "def create_mood(item: dict):\n", "    global mood_counter\n", "    mood_counter += 1\n", "\n", "    # 这是一个“定时炸弹”\n", "    new_mood = {\n", "        \"id\": mood_counter,\n", "        \"user\": item[\"user\"], # 如果请求里没有\"user\"键,这里会崩溃!\n", "        \"mood\": item[\"mood\"], # 如果请求里没有\"mood\"键,这里也会崩溃!\n", "    }\n", "\n", "    mood_items.append(new_mood)\n", "    return new_mood\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 实战演练：当一次“黑客”，让服务崩溃！\n", "这个API看起来能工作，但它有一个致命的弱点：它盲目信任所有用户都会发送正确格式的数据。现在，让我们故意发送一个错误的数据，看看会发生什么。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在发送一个格式错误的请求...\n", "响应状态码: 500\n", "响应内容: Internal Server Error\n"]}], "source": ["# 确保你的FastAPI服务器正在运行新的代码\n", "import requests\n", "\n", "# 假设你的API部署在 127.0.0.1:8000\n", "API_URL = \"http://127.0.0.1:8000/moods\"\n", "\n", "# 故意用错的键名 'name' 而不是 'user'\n", "payload = {\"name\": \"捣蛋鬼\", \"mood\": \"hahaha\"}\n", "\n", "print(\"正在发送一个格式错误的请求...\")\n", "try:\n", "    response = requests.post(API_URL, json=payload)\n", "    print(\"响应状态码:\", response.status_code)\n", "    print(\"响应内容:\", response.text)\n", "except requests.ConnectionError:\n", "    print(\"连接服务器失败！\")\n"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### 演练结果分析\n", "你会看到服务器返回了 `500 Internal Server Error`（服务器内部错误）。\n", "**这意味着什么？**\n", "\n", "仅仅是一个小小的字段名错误，就让我们的整个服务崩溃了！在真实世界里，这意味着你的网站或App对**所有**用户都下线了。这就是我们必须解决的第一个痛点：**健壮性**。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 解药一：FastAPI的武器 - 优雅地抛出HTTP错误"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["当我们的程序遇到可预见的错误（比如用户没提供必要的字段）时，我们不应该让它崩溃，而是应该**主动**告诉客户端：“你的请求错了！”并返回一个清晰的错误信息。\n", "\n", "FastAPI提供了`HTTPException`这个强大的武器，让我们能轻松做到这一点。\n", "\n", "**修改`main.py`中的`create_mood`函数：**\n", "```python\n", "from fastapi import FastAPI, HTTPException # 别忘了导入HTTPException\n", "\n", "# ... 和上面一样\n", "@app.post(\"/moods\")\n", "def create_mood(item: dict):\n", "    # 在真正处理数据前，先做检查\n", "    if \"user\" not in item or \"mood\" not in item:\n", "        # 如果缺少字段，就抛出HTTPException\n", "        raise HTTPException(\n", "            status_code=400, # 400表示“错误的请求”\n", "            detail=\"请求体必须包含 'user' 和 'mood' 字段\"\n", "        )\n", "    # ... (其他不变) ...\n", "```\n", "现在，再用刚才的“黑客”代码攻击一次，看看返回结果有什么不同？你会收到一个清晰的`400`错误和友好的提示，而服务器本身安然无恙！"]}, {"cell_type": "markdown", "id": "3ef6d51a", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 深度思考：`raise HTTPException` vs `try...except`\n", "\n", "看到 `raise`，你可能会想：“我可以用 `try...except` 来捕获错误，不也能防止程序崩溃吗？为什么非要用 `raise` 呢？”\n", "\n", "这是一个非常好的问题！它们都能防止程序崩溃，但处理的是完全不同类型的“错误”，代表了两种不同的编程思想。\n", "\n", "**`try...except`：处理“意外事故”**\n", "\n", "-   **场景**：当你调用一段**你无法完全控制**的代码，或者当程序遇到**不可预见的运行时问题**时使用。比如，你期望用户输入一个数字，结果他输入了文字，导致 `int()` 函数抛出 `ValueError`；或者你要读取一个文件，结果文件不存在，抛出 `FileNotFoundError`。\n", "-   **语义**：是一种**被动防御**。你在说：“我预感到这里可能会出问题，如果真的出事了，我要这样补救，但程序要继续运行。”\n", "-   **例子**：我们可以在“脆弱”的代码外层包上`try...except`来捕获底层的`KeyError`。\n", "    ```python\n", "    try:\n", "        # \"脆弱\"的代码，item[\"user\"] 可能会引发 KeyError\n", "        user = item[\"user\"] \n", "    except KeyError:\n", "        # 捕获到意外的 KeyError 后，我们决定返回一个标准的客户端错误\n", "        raise HTTPException(status_code=400, detail=\"请求体中缺少 'user' 字段\")\n", "    ```\n", "    注意，这里我们是在 `except` 块里**再** `raise` 一个 `HTTPException`，将程序内部错误转换为一个对客户端友好的API错误。"]}, {"cell_type": "markdown", "id": "71d5a2de", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**`if ... raise HTTPException`：主动宣告“业务规则错误”**\n", "\n", "-   **场景**：当你检查的条件是**由你的业务逻辑定义**的，并且这个条件未被满足时使用。比如，“用户名不能少于3个字符”、“订单金额必须大于0”、“该用户不存在”等。这些都不是程序运行时的意外（不是`KeyError`或`TypeError`），而是**客户端没有遵守API规则**。\n", "-   **语义**：是一种**主动断言**。你在说：“根据我的业务规则，你的请求是无效的。我明确地告诉你错在哪里，并终止当前这次请求的处理。”\n", "-   **例子**：\n", "    ```python\n", "    # 在我们的 get_user_moods 函数中\n", "    user_moods = [item for item in mood_items if item[\"user\"] == user_name]\n", "    if not user_moods:\n", "        # 这不是程序意外，而是业务上“查无此人”的明确结果\n", "        raise HTTPException(status_code=404, detail=f\"用户 '{user_name}' 未找到\")\n", "    ```"]}, {"cell_type": "markdown", "id": "40d956b2", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**总结**\n", "\n", "| 特性 | `try...except` | `if ... raise HTTPException` |\n", "| :--- | :--- | :--- |\n", "| **目的** | 捕获并处理**程序意外**（如`KeyError`, `TypeError`） | 主动拒绝**不符合业务规则**的请求（如用户不存在） |\n", "| **性质** | 被动、防御性 | 主动、断言性 |\n", "| **核心** | “我不知道会出什么错，但出错了得兜着” | “我知道这个请求错在哪，我要明确地告诉他” |\n", "\n", "在开发API时，我们更推崇**先用 `Pydantic` 和 `if` 条件判断来处理所有可预见的客户端错误（业务错误），并主动 `raise HTTPException`**。这样代码更清晰，意图更明确，也让框架能更好地生成API文档。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 解药二：FastAPI的超级武器 - 用`Pydantic`进行数据校验\n", "\n", "*小插曲：什么是类型提示 (Type Hinting)？*\n", "\n", "在进入Pydantic的世界之前，我们需要先了解一个Python 3.5+ 版本引入的重要特性：类型提示。\n", "\n", "简单来说，它允许我们在代码中“标注”一个变量、函数参数或返回值应该是什么数据类型。比如：\n", "\n", "```python\n", "# 传统写法\n", "def say_hello(name):\n", "    return \"Hello, \" + name\n", " \n", "# 使用类型提示的写法\n", "def say_hello(name: str) -> str:\n", "    return \"Hello, \" + name\n", "```\n", "\n", "`name: str` 表示我们期望 `name` 参数是一个字符串。`-> str` 表示我们期望这个函数返回一个字符串。"]}, {"cell_type": "markdown", "id": "43000447", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 这有什么好处？\n", "\n", "代码更易读、易维护：其他人（或未来的你）一眼就能看出函数需要什么、返回什么。\n", "编辑器和工具支持：像VS Code这样的现代编辑器会根据类型提示提供更智能的自动补全和错误检查。\n", "\n", "FastAPI 和 Pydantic 的基石：FastAPI 和 Pydantic 会在运行时读取这些类型提示，并自动进行数据校验和转换！这是它们实现自动化魔法的核心。\n", "\n", "在我们的API中，我们用到了 typing 模块里的一些特殊类型：\n", "\n", "- `List[SomeType]`: 表示一个列表，且列表中的每个元素都应该是 `SomeType` 类型。例如 `List[str]` 就是一个字符串列表。\n", "\n", "- `Optional[SomeType]`: 表示这个字段是可选的。它的值可以是 `SomeType`，也可以是 `None`。这在定义非必填字段时非常有用。\n", "\n", "了解了这一点，我们再来看Pydantic就会豁然开朗。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 新的痛点：繁琐的数据校验\n", "刚才的`if`判断虽然解决了问题，但想象一下：\n", "- 如果有10个必填字段，难道要写10个`if`判断吗？\n", "- 如果用户传来的`user`不是字符串，而是一个数字`123`怎么办？我们还没检查类型呢！\n", "- 代码会变得越来越臃肿，难以维护。\n", "\n", "**有没有更优雅、更强大的方法呢？**"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### FastAPI的数据模型 `BaseModel`\n", "FastAPI集成的`Pydantic`库提供了一个`BaseModel`，它就像一个“超级蓝图”。我们只需要用它来定义一个我们期望的数据模型（一个类），FastAPI就会自动帮我们完成所有的数据校验！\n", "\n", "**修改`main.py`：**\n", "\n", "```python\n", "from fastapi import FastAPI, HTTPException\n", "from pydantic import BaseModel # 导入BaseModel\n", "from typing import Optional, List # 导入用于类型提示的工具\n", "\n", "mood_items = []\n", "\n", "# 1. 定义一个用于创建心情记录的数据模型\n", "# 我们期望用户POST过来的JSON必须符合这个结构\n", "class MoodItemCreate(BaseModel):\n", "    user: str                # user字段必须是字符串\n", "    mood: str                # mood字段必须是字符串\n", "    message: Optional[str] = None # message字段是可选的字符串\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 用数据模型重构API\n", "现在，我们用定义好的`MoodItemCreate`模型来重构我们的`create_mood`函数。\n", "\n", "```python\n", "# 2. 修改函数的参数类型提示\n", "# 把 item: dict 改为 item: MoodItemCreate\n", "@app.post(\"/moods\")\n", "def create_mood(item: MoodItemCreate):\n", "    # 魔法发生了！\n", "    # FastAPI会自动检查请求体是否符合MoodItemCreate的定义。\n", "    # 如果不符合（比如缺少字段、类型错误），它会自动返回一个清晰的422错误！\n", "    # 我们再也不需要写那些恶心的if校验了！\n", "\n", "    global mood_counter\n", "    mood_counter += 1\n", "\n", "    # 这里的item已经是Pydantic模型对象，我们可以像访问对象属性一样访问它\n", "    new_mood = {\n", "        \"id\": mood_counter,\n", "        \"user\": item.user,\n", "        \"mood\": item.mood,\n", "        \"message\": item.message\n", "    }\n", "\n", "    mood_items.append(new_mood)\n", "    return new_mood\n", "```\n", "代码是不是变得干净、专注、易读多了？这就是数据模型的威力！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 完善我们的心情记录API"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["现在我们的`POST`端点已经非常健壮了，我们来完成另外两个`GET`端点，并为它们也定义好返回数据模型，这样API文档会更清晰。\n", "\n", "**继续在`main.py`中添加：**\n", "\n", "```python\n", "# 定义一个用于API响应的数据模型，它比创建模型多了个id字段\n", "class MoodItemResponse(BaseModel):\n", "    id: int\n", "    user: str\n", "    mood: str\n", "    message: Optional[str] = None\n", "\n", "# 修改POST端点的装饰器，告诉FastAPI响应应该符合什么模型\n", "@app.post(\"/moods\", response_model=MoodItemResponse)\n", "def create_mood(item: MoodItemCreate):\n", "    # ... (函数体不变) ...\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**添加GET端点代码：**\n", "```python\n", "# 1. 获取所有心情记录\n", "# response_model=List[MoodItemResponse] 表示响应是一个包含多个MoodItemResponse对象的列表\n", "@app.get(\"/moods\", response_model=List[MoodItemResponse])\n", "def get_all_moods():\n", "    \"\"\"获取系统中保存的所有心情记录。\"\"\" # 这个是文档字符串(docstring)\n", "    return mood_items"]}, {"cell_type": "markdown", "id": "b1de9966", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "# 2. 获取特定用户的心情记录\n", "# {user_name} 是一个路径参数，FastAPI会自动把它作为函数的参数传入\n", "@app.get(\"/moods/{user_name}\", response_model=List[MoodItemResponse])\n", "def get_user_moods(user_name: str):\n", "    \"\"\"\n", "    获取指定用户的所有心情记录。\n", "    - **user_name**: 要查询的用户名。\n", "    \"\"\"\n", "    user_moods = [item for item in mood_items if item[\"user\"] == user_name]\n", "    if not user_moods:\n", "        # 如果用户不存在，返回404错误\n", "        raise HTTPException(status_code=404, detail=f\"用户 '{user_name}' 未找到\")\n", "    return user_moods\n", "```\n", "**提示**：函数开头的三引号字符串`\"\"\"...\"\"\"`叫做**docstring**，FastAPI会自动把它抓取并显示在API文档里，让你的文档更友好！"]}], "metadata": {"kernelspec": {"display_name": "XFrame", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}