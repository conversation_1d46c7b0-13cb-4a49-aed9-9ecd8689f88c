import requests
import json

# 1. 使用 input() 函数从用户那里获取城市名（拼音）
city = input("请输入城市拼音 (例如 beijing): ")

# 2. 使用 f-string 构建完整的API请求URL
url = f"https://goweather.xyz/weather/{city}"

try:
    # 3. 发送GET请求
    response = requests.get(url)
    # 这是一个非常好的习惯：如果请求失败(例如城市不存在导致404)，程序会在这里抛出异常
    response.raise_for_status()

    # 4. 如果请求成功，就解析JSON数据
    weather_data = response.json()
    print("成功获取天气数据:")
    print(json.dumps(weather_data, indent=4, ensure_ascii=False, sort_keys=True))

except requests.exceptions.RequestException as e:
    # 捕获所有可能的网络请求错误
    print(f"获取天气失败: {e}")
    # 在失败时创建一个空字典，保证后续代码不会因变量不存在而报错
    weather_data = {} 

# 确保上一步获取数据成功，weather_data不为空
if weather_data:
    # 使用 .get() 方法安全地获取数据。如果'temperature'键不存在，它会返回默认值'0 °C'，避免程序出错
    temp_str = weather_data.get('temperature', '0 °C')
    wind_str = weather_data.get('wind', '0 km/h')
    description = weather_data.get('description', '未知')
    
    try:
        # --- 数据清洗 --- 
        # 对温度字符串进行处理
        temp_value = int(temp_str.split()[0])
        # 对风速字符串进行处理
        wind_value = int(wind_str.split()[0])

        print(f"处理后的温度: {temp_value}")
        print(f"处理后的风速: {wind_value}")

    except (ValueError, IndexError):
        # 如果split或int转换失败（例如返回的数据格式异常），给出提示
        print("无法从返回数据中解析出有效的温度或风速数值。")
        # 提供默认值以防后续代码报错
        temp_value, wind_value = 0, 0 
else:
    print("没有天气数据可供处理。")

# 定义一个专门提供穿衣建议的函数
def get_clothing_advice(temp, wind):
    """根据温度和风速返回穿衣建议的字符串"""
    # 1. 首先根据温度确定基础建议
    if temp < 5:
        base_advice = "天气寒冷，建议穿厚羽绒服、毛衣、保暖内衣。"
    elif 5 <= temp < 15:
        base_advice = "天气凉爽，建议穿外套、长袖衫、长裤。"
    elif 15 <= temp < 25:
        base_advice = "天气温和，建议穿薄外套或针织衫、T恤。"
    else:  # temp >= 25
        base_advice = "天气炎热，建议穿T恤、短裤，别忘了防晒！"
    
    # 2. 接着根据风速（单位：千米/小时）增加额外的防风建议
    #    这里我们定义一些风速阈值，可以根据实际需求调整
    #    - 18 km/h (约等于3级风) 以下，风力较小
    #    - 18-36 km/h (约等于4-5级风)，有明显风感
    #    - 36 km/h (约等于6级风) 以上，风力很大
    wind_advice = ""
    if wind >= 36:
        wind_advice = "风力强劲，务必穿着防风外套，并注意保护头部和颈部，例如戴帽子和围巾。"
    elif wind >= 18:
        wind_advice = "有明显风感，建议选择一件防风的外套。"
 
    # 3. 组合最终建议，并使用 strip() 清理可能多余的空格
    final_advice = (base_advice + " " + wind_advice).strip()
 
    return final_advice

# 确保前面的变量已成功创建
if 'temp_value' in locals():
    # 调用函数获取建议
    advice = get_clothing_advice(temp_value, wind_value)
    
    # 格式化打印完整的天气报告
    print(f"\n--- 天气报告 ---")
    print(f"城市: {city.capitalize()}") # .capitalize()让首字母大写
    print(f"天气: {description}")
    print(f"温度: {temp_str}")
    print(f"风速: {wind_str}")
    print(f"穿衣建议: {advice}")
else:
    print("无法生成穿衣建议，因为前面的步骤失败了。")

# 步骤1: 封装查询逻辑为一个函数, 这是良好编程习惯的开始
def get_weather(city):
    url = f"https://goweather.xyz/weather/{city}"
    try:
        response = requests.get(url)
        response.raise_for_status() # 如果请求失败 (如 404, 500), 会抛出异常
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"获取 {city} 天气失败: {e}")
        return None # 返回 None 表示失败，方便后续判断

# 步骤2: 创建要查询的城市列表
cities_to_check = ['Beijing', 'Shanghai', 'Tokyo', 'London']
print("--- 多城市天气速览 ---")

# 步骤3: 循环遍历列表
for city_name in cities_to_check:
    weather_data_loop = get_weather(city_name)
    # 步骤4: 处理并打印结果
    if weather_data_loop:
        # 我们只关心当前温度和描述
        temperature = weather_data_loop.get('temperature', 'N/A')
        description = weather_data_loop.get('description', 'N/A')
        # 使用格式化字符串，让输出更整齐
        # {city_name:<10} 表示左对齐，占据10个字符宽度
        print(f"{city_name:<10} | 温度: {temperature:<8} | 天气: {description}")

import matplotlib.pyplot as plt

# 1. 获取单个城市的天气数据
city_to_plot = "Tsuchiura"
weather_data_plot = get_weather(city_to_plot) # 复用上面的 get_weather 函数

# 健壮性检查：确保数据有效
if not weather_data_plot or 'forecast' not in weather_data_plot or not weather_data_plot['forecast']:
    print(f"无法获取 {city_to_plot} 的预报数据用于绘图。")
else:
    # 2. 从JSON中提取出`forecast`部分
    forecast_data = weather_data_plot['forecast']
    
    # 3. 准备绘图所需的X轴和Y轴数据列表
    days = [f"Day {item['day']}" for item in forecast_data] # 生成X轴标签
    temperatures = []
    for item in forecast_data:
        try:
            # 3a. 清洗温度数据：去掉'°C'和前后空格，然后转为整数
            temp_str = item.get('temperature', '0 °C').replace('°C', '').strip()
            temperatures.append(int(temp_str))
        except (ValueError, IndexError):
            temperatures.append(0) # 如果数据格式不正确，则添加0
    
    # 4. 开始绘图
    plt.figure(figsize=(8, 5)) # 创建一个 8x5 英寸的画布
    plt.plot(days, temperatures, marker='o', linestyle='-') # 绘制带圆点标记的折线图
    plt.title(f'{city_to_plot} 3-Day Temperature Trend') # 设置标题
    plt.xlabel('Date') # 设置X轴标签
    plt.ylabel('Temperature (°C)') # 设置Y轴标签
    plt.grid(True) # 显示网格线，方便读数

    # 在每个数据点上显示具体的温度数值
    for i, temp in enumerate(temperatures):
        plt.text(days[i], temp + 0.5, f' {temp}°C') # 向上偏移0.5个单位显示文本，避免重叠

    plt.show() # 显示最终的图表

# 新建一个专门用来发送Bark通知的函数
def send_bark_notification(key, title, body):
    """使用 Bark App 发送一条推送通知"""
    # 检查用户是否已替换自己的密钥
    if not key or key.startswith("YOUR") or len(key) < 10:
        print(f"[演示模式]\n[标题] {title}\n[内容] {body}")
        return
        
    # Bark API 支持将标题和内容直接放在URL中，非常方便
    url = f"https://api.day.app/{key}/{title}/{body}"
    try:
        print("正在发送天气预报到你的手机...")
        response = requests.get(url)
        response.raise_for_status() # 检查是否发送成功
        print("天气预报通知已成功发送! 请在你的手机上查看。")
    except requests.exceptions.RequestException as e:
        print(f"发送通知失败: {e}")

# --- 主程序区域 ---
# --- 配置 --- 
BARK_KEY = "kCd2MErsZreF2nQxmgCBSk" # !!重要!! 请将这里替换成你自己的 Bark Key
city_to_notify = "Tsuchiura"

# 1. 获取天气数据
weather_data = get_weather(city_to_notify) # 复用我们的函数

if weather_data:
    # 2. 解析和处理数据
    temp_str = weather_data.get('temperature', 'N/A')
    wind_str = weather_data.get('wind', 'N/A')
    description = weather_data.get('description', '未知')
    temp_value = int(temp_str.split()[0]) if temp_str != 'N/A' else 0
    wind_value = int(weather_data.get('wind', '0 km/h').split()[0]) if wind_str != 'N/A' else 0

    # 3. 生成穿衣建议
    advice = get_clothing_advice(temp_value, wind_value) # 复用建议函数 (风速暂时忽略)
    
    # 4. 构建通知的标题和内容
    notification_title = f"{city_to_notify}天气速报 - {temp_str}"
    notification_body = f"今天天气{description}，{advice}"
    
    # 5. 发送通知!
    send_bark_notification(BARK_KEY, notification_title, notification_body)

def send_weather_notification_with_sound(bark_key, city, weather_data):
    """根据天气情况发送带有不同声音的通知"""
    if not weather_data:
        return
    
    temp_str = weather_data.get('temperature', 'N/A')
    description = weather_data.get('description', '未知')
    
    # 根据天气描述选择不同的声音
    if 'rain' in description.lower() or 'storm' in description.lower():
        sound = 'telegraph'  # 雨天用电报声
    elif 'sunny' in description.lower() or 'clear' in description.lower():
        sound = 'bell'       # 晴天用铃声
    elif 'cloud' in description.lower():
        sound = 'glass'      # 多云用玻璃声
    else:
        sound = 'minuet'     # 其他情况用小步舞曲
    
    # 构建带声音的URL
    title = f"{city}天气播报"
    body = f"当前温度{temp_str}，天气{description}"
    
    # Bark的声音参数格式：?sound=声音名称
    url = f"https://api.day.app/{bark_key}/{title}/{body}?sound={sound}"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            print(f"✅ 天气通知已发送到手机，使用声音: {sound}")
        else:
            print(f"❌ 发送失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 发送出错: {e}")

send_weather_notification_with_sound(BARK_KEY, "Tsuchiura", get_weather("Tsuchiura"))

def send_weather_notification_with_icon(bark_key, city, weather_data):
    """发送带有天气图标的通知"""
    if not weather_data:
        return
    
    temp_str = weather_data.get('temperature', 'N/A')
    description = weather_data.get('description', '未知')
    
    # 根据天气选择图标
    if 'rain' in description.lower():
        icon = 'https://cdn-icons-png.flaticon.com/512/3313/3313998.png'  # 雨天图标
    elif 'sunny' in description.lower() or 'clear' in description.lower():
        icon = 'https://cdn-icons-png.flaticon.com/512/869/869869.png'   # 晴天图标
    elif 'cloud' in description.lower():
        icon = 'https://cdn-icons-png.flaticon.com/512/414/414927.png'   # 多云图标
    else:
        icon = 'https://cdn-icons-png.flaticon.com/512/1163/1163661.png' # 默认天气图标
    
    title = f"{city}天气预报"
    body = f"温度: {temp_str}\n天气: {description}"
    
    # 使用icon参数设置图标
    url = f"https://api.day.app/{bark_key}/{title}/{body}?icon={icon}"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            print(f"✅ 带图标的天气通知已发送")
        else:
            print(f"❌ 发送失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 发送出错: {e}")

send_weather_notification_with_icon(BARK_KEY, "Tsuchiura", get_weather("Tsuchiura"))

import time
from datetime import datetime

def schedule_daily_weather_report(bark_key, city, target_hour=8):
    """定时发送每日天气播报"""
    print(f"⏰ 定时天气播报已启动，每天{target_hour}点发送{city}的天气")
    
    while True:
        now = datetime.now()
        
        # 检查是否到了播报时间
        if now.hour == target_hour and now.minute == 0:
            print(f"📢 开始发送{city}的每日天气播报...")
            
            # 获取天气数据
            weather_data = get_weather(city)
            
            if weather_data:
                temp_str = weather_data.get('temperature', 'N/A')
                description = weather_data.get('description', '未知')
                wind_str = weather_data.get('wind', 'N/A')
                
                # 生成穿衣建议
                temp_value = int(temp_str.split()[0]) if temp_str != 'N/A' else 0
                wind_value = int(wind_str.split()[0]) if wind_str != 'N/A' else 0
                advice = get_clothing_advice(temp_value, wind_value)
                
                # 发送早安天气播报
                title = f"☀️ 早安！{city}天气播报"
                body = f"今天{description}，温度{temp_str}\n{advice}\n祝你有美好的一天！"
                
                send_bark_notification(bark_key, title, body)
                
                # 等待61秒，避免重复发送
                time.sleep(61)
        
        # 每分钟检查一次
        time.sleep(60)

def advanced_weather_assistant(bark_key, city):
    """高级天气助手 - 综合所有Bark功能"""
    weather_data = get_weather(city)
    
    if not weather_data:
        print(f"❌ 无法获取{city}的天气数据")
        return
    
    temp_str = weather_data.get('temperature', 'N/A')
    description = weather_data.get('description', '未知')
    wind_str = weather_data.get('wind', 'N/A')
    
    # 解析温度和风速
    temp_value = int(temp_str.split()[0]) if temp_str != 'N/A' else 0
    wind_value = int(wind_str.split()[0]) if wind_str != 'N/A' else 0
    
    # 生成建议
    advice = get_clothing_advice(temp_value, wind_value)
    
    # 根据温度选择不同的通知样式
    if temp_value >= 30:
        # 高温警告 - 使用警告声音和红色图标
        title = f"🌡️ 高温预警 - {city}"
        body = f"温度高达{temp_str}！\n{advice}\n请注意防暑降温！"
        sound = 'alarm'
        icon = 'https://cdn-icons-png.flaticon.com/512/1684/1684350.png'
    elif temp_value <= 0:
        # 低温提醒 - 使用提醒声音和蓝色图标
        title = f"❄️ 低温提醒 - {city}"
        body = f"温度仅{temp_str}，天气寒冷\n{advice}\n请注意保暖！"
        sound = 'bell'
        icon = 'https://cdn-icons-png.flaticon.com/512/2336/2336776.png'
    else:
        # 普通天气 - 使用温和声音和天气图标
        title = f"🌤️ {city}天气播报"
        body = f"今天{description}，温度{temp_str}\n{advice}"
        sound = 'minuet'
        icon = 'https://cdn-icons-png.flaticon.com/512/1163/1163661.png'
    
    # 发送综合通知
    url = f"https://api.day.app/{bark_key}/{title}/{body}?sound={sound}&icon={icon}"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            print(f"✅ 高级天气助手通知已发送！")
            print(f"📱 标题: {title}")
            print(f"📝 内容: {body}")
            print(f"🔊 声音: {sound}")
        else:
            print(f"❌ 发送失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 发送出错: {e}")

advanced_weather_assistant(BARK_KEY, "Tsuchiura")