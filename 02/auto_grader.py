"""
自动测试评分系统 - 外汇交易机会分析版
用于评估 practice_hard.py 的完成质量

评分标准：
- 基础功能实现 (40分)
- 交易分析功能 (30分)
- 错误处理完善 (15分)
- 代码结构清晰 (10分)
- 挑战功能实现 (5分)
"""

import importlib.util
import sys
import io
import contextlib
import traceback
import inspect
import requests
from datetime import datetime
import matplotlib.pyplot as plt

class ForexTradingGrader:
    def __init__(self, student_file="practice_hard.py"):
        self.student_file = student_file
        self.total_score = 0
        self.max_score = 100
        self.test_results = []
        self.student_module = None
        
    def load_student_code(self):
        """加载学生的代码文件"""
        try:
            spec = importlib.util.spec_from_file_location("student_code", self.student_file)
            self.student_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(self.student_module)
            return True
        except Exception as e:
            self.add_result("代码加载", 0, 5, f"无法加载代码文件: {e}")
            return False
    
    def add_result(self, test_name, score, max_score, comment=""):
        """添加测试结果"""
        self.test_results.append({
            'name': test_name,
            'score': score,
            'max_score': max_score,
            'comment': comment
        })
        self.total_score += score
    
    def test_class_structure(self):
        """测试类结构 (15分)"""
        try:
            if hasattr(self.student_module, 'ForexTradingAnalyzer'):
                analyzer_class = self.student_module.ForexTradingAnalyzer

                # 检查基础必要的方法
                required_methods = [
                    '__init__', 'get_current_rate', 'get_historical_rates',
                    'parse_data', 'create_chart', 'analyze_rates', 'run'
                ]

                # 检查交易分析方法
                trading_methods = [
                    'find_historical_lows', 'find_trading_opportunities',
                    'calculate_arbitrage', 'compare_profits', 'recommend_strategy'
                ]

                missing_basic = []
                missing_trading = []

                for method in required_methods:
                    if not hasattr(analyzer_class, method):
                        missing_basic.append(method)

                for method in trading_methods:
                    if not hasattr(analyzer_class, method):
                        missing_trading.append(method)

                score = 15
                if missing_basic:
                    score -= len(missing_basic) * 2
                if missing_trading:
                    score -= len(missing_trading) * 1

                score = max(0, score)

                if score == 15:
                    self.add_result("类结构", 15, 15, "所有必要方法都已定义")
                else:
                    comment = ""
                    if missing_basic:
                        comment += f"缺少基础方法: {', '.join(missing_basic)}; "
                    if missing_trading:
                        comment += f"缺少交易方法: {', '.join(missing_trading)}"
                    self.add_result("类结构", score, 15, comment)
            else:
                self.add_result("类结构", 0, 15, "未找到ForexTradingAnalyzer类")
        except Exception as e:
            self.add_result("类结构", 0, 15, f"测试类结构时出错: {e}")
    
    def test_initialization(self):
        """测试初始化方法 (10分)"""
        try:
            if hasattr(self.student_module, 'ForexTradingAnalyzer'):
                analyzer = self.student_module.ForexTradingAnalyzer()

                # 检查是否有基本属性
                if hasattr(analyzer, '__dict__') and analyzer.__dict__:
                    self.add_result("初始化", 10, 10, "初始化方法正常工作")
                else:
                    self.add_result("初始化", 5, 10, "初始化方法存在但可能不完整")
            else:
                self.add_result("初始化", 0, 10, "无法测试初始化")
        except Exception as e:
            self.add_result("初始化", 0, 10, f"初始化测试失败: {e}")
    
    def test_current_rate_method(self):
        """测试获取当前汇率方法 (10分)"""
        try:
            if hasattr(self.student_module, 'ForexTradingAnalyzer'):
                analyzer = self.student_module.ForexTradingAnalyzer()

                if hasattr(analyzer, 'get_current_rate'):
                    # 尝试调用方法
                    result = analyzer.get_current_rate()

                    if result is not None and isinstance(result, (int, float)):
                        self.add_result("获取当前汇率", 10, 10, "方法正常工作并返回数值")
                    elif result is not None:
                        self.add_result("获取当前汇率", 7, 10, "方法工作但返回类型可能不正确")
                    else:
                        self.add_result("获取当前汇率", 3, 10, "方法存在但返回None")
                else:
                    self.add_result("获取当前汇率", 0, 10, "未实现get_current_rate方法")
            else:
                self.add_result("获取当前汇率", 0, 10, "无法测试")
        except Exception as e:
            self.add_result("获取当前汇率", 0, 10, f"测试失败: {str(e)[:100]}")
    
    def test_historical_data_method(self):
        """测试获取历史数据方法 (10分)"""
        try:
            if hasattr(self.student_module, 'ForexTradingAnalyzer'):
                analyzer = self.student_module.ForexTradingAnalyzer()

                if hasattr(analyzer, 'get_historical_rates'):
                    result = analyzer.get_historical_rates()

                    if isinstance(result, tuple) and len(result) == 2:
                        dates, rates = result
                        if dates and rates and len(dates) == len(rates):
                            self.add_result("获取历史数据", 10, 10, "方法正常工作并返回正确格式数据")
                        else:
                            self.add_result("获取历史数据", 7, 10, "方法返回元组但数据可能不完整")
                    else:
                        self.add_result("获取历史数据", 3, 10, "方法存在但返回格式不正确")
                else:
                    self.add_result("获取历史数据", 0, 10, "未实现get_historical_rates方法")
            else:
                self.add_result("获取历史数据", 0, 10, "无法测试")
        except Exception as e:
            self.add_result("获取历史数据", 0, 10, f"测试失败: {str(e)[:100]}")
    
    def test_chart_creation(self):
        """测试图表创建方法 (10分)"""
        try:
            if hasattr(self.student_module, 'ForexTradingAnalyzer'):
                analyzer = self.student_module.ForexTradingAnalyzer()

                if hasattr(analyzer, 'create_chart'):
                    # 使用测试数据
                    test_dates = ['2025-06-20', '2025-06-21', '2025-06-22']
                    test_rates = [0.045, 0.046, 0.044]

                    # 捕获matplotlib输出
                    plt.ioff()  # 关闭交互模式
                    analyzer.create_chart(test_dates, test_rates)
                    plt.close('all')  # 关闭所有图表

                    self.add_result("图表创建", 10, 10, "图表创建方法正常工作")
                else:
                    self.add_result("图表创建", 0, 10, "未实现create_chart方法")
            else:
                self.add_result("图表创建", 0, 10, "无法测试")
        except Exception as e:
            self.add_result("图表创建", 3, 10, f"图表创建可能有问题: {str(e)[:100]}")

    def test_trading_analysis_methods(self):
        """测试交易分析方法 (30分)"""
        try:
            if hasattr(self.student_module, 'ForexTradingAnalyzer'):
                analyzer = self.student_module.ForexTradingAnalyzer()

                # 测试史低价格发现 (10分)
                if hasattr(analyzer, 'find_historical_lows'):
                    try:
                        result = analyzer.find_historical_lows()
                        if isinstance(result, dict) and any(key in result for key in ['3_year_low', '5_year_low']):
                            self.add_result("史低价格发现", 10, 10, "史低价格分析方法正常工作")
                        else:
                            self.add_result("史低价格发现", 5, 10, "方法存在但返回格式可能不正确")
                    except:
                        self.add_result("史低价格发现", 2, 10, "方法存在但执行有问题")
                else:
                    self.add_result("史低价格发现", 0, 10, "未实现find_historical_lows方法")

                # 测试交易机会分析 (10分)
                if hasattr(analyzer, 'find_trading_opportunities'):
                    try:
                        result = analyzer.find_trading_opportunities()
                        if isinstance(result, dict) and any(key in result for key in ['buy_point', 'sell_point', 'profit_rate']):
                            self.add_result("交易机会分析", 10, 10, "交易机会分析方法正常工作")
                        else:
                            self.add_result("交易机会分析", 5, 10, "方法存在但返回格式可能不正确")
                    except:
                        self.add_result("交易机会分析", 2, 10, "方法存在但执行有问题")
                else:
                    self.add_result("交易机会分析", 0, 10, "未实现find_trading_opportunities方法")

                # 测试三角套利计算 (10分)
                if hasattr(analyzer, 'calculate_arbitrage'):
                    try:
                        result = analyzer.calculate_arbitrage()
                        if isinstance(result, dict) and any(key in result for key in ['direct_path', 'arbitrage_path', 'arbitrage_profit']):
                            self.add_result("三角套利计算", 10, 10, "三角套利分析方法正常工作")
                        else:
                            self.add_result("三角套利计算", 5, 10, "方法存在但返回格式可能不正确")
                    except:
                        self.add_result("三角套利计算", 2, 10, "方法存在但执行有问题")
                else:
                    self.add_result("三角套利计算", 0, 10, "未实现calculate_arbitrage方法")
            else:
                self.add_result("史低价格发现", 0, 10, "无法测试")
                self.add_result("交易机会分析", 0, 10, "无法测试")
                self.add_result("三角套利计算", 0, 10, "无法测试")
        except Exception as e:
            self.add_result("交易分析功能", 0, 30, f"测试交易分析功能时出错: {e}")

    def test_error_handling(self):
        """测试错误处理 (15分)"""
        score = 0

        # 检查是否有try-except块
        try:
            with open(self.student_file, 'r', encoding='utf-8') as f:
                content = f.read()

            if 'try:' in content and 'except' in content:
                score += 8
                self.add_result("错误处理-语法", 8, 8, "代码中包含错误处理结构")
            else:
                self.add_result("错误处理-语法", 0, 8, "代码中缺少错误处理结构")

            # 测试实际错误处理
            if hasattr(self.student_module, 'ForexTradingAnalyzer'):
                analyzer = self.student_module.ForexTradingAnalyzer()

                # 尝试用无效数据测试
                if hasattr(analyzer, 'parse_data'):
                    try:
                        result = analyzer.parse_data(None)
                        score += 7
                        self.add_result("错误处理-实际", 7, 7, "错误处理实现正常")
                    except:
                        self.add_result("错误处理-实际", 3, 7, "错误处理可能不完善")
                else:
                    self.add_result("错误处理-实际", 0, 7, "无法测试实际错误处理")
            else:
                self.add_result("错误处理-实际", 0, 7, "无法测试")

        except Exception as e:
            self.add_result("错误处理", 0, 15, f"测试错误处理时出错: {e}")
    
    def test_bonus_features(self):
        """测试挑战功能 (5分)"""
        score = 0

        try:
            if hasattr(self.student_module, 'ForexTradingAnalyzer'):
                analyzer = self.student_module.ForexTradingAnalyzer()

                # 检查挑战功能方法
                bonus_methods = ['save_data', 'set_alert', 'display_trading_menu', 'compare_profits', 'recommend_strategy']
                implemented = []

                for method in bonus_methods:
                    if hasattr(analyzer, method):
                        implemented.append(method)
                        score += 1

                if implemented:
                    self.add_result("挑战功能", min(score, 5), 5, f"实现了: {', '.join(implemented)}")
                else:
                    self.add_result("挑战功能", 0, 5, "未实现挑战功能")
            else:
                self.add_result("挑战功能", 0, 5, "无法测试")
        except Exception as e:
            self.add_result("挑战功能", 0, 5, f"测试挑战功能时出错: {e}")
    
    def generate_report(self):
        """生成评分报告"""
        print("=" * 70)
        print("外汇交易机会分析程序 - 自动评分报告")
        print("=" * 70)
        
        for result in self.test_results:
            status = "通过" if result['score'] == result['max_score'] else "部分通过" if result['score'] > 0 else "未通过"
            print(f"{result['name']:<15} {result['score']:>3}/{result['max_score']:<3} [{status}] {result['comment']}")
        
        print("-" * 60)
        print(f"总分: {self.total_score}/{self.max_score}")
        
        # 等级评定
        percentage = (self.total_score / self.max_score) * 100
        if percentage >= 90:
            grade = "优秀"
        elif percentage >= 80:
            grade = "良好"
        elif percentage >= 70:
            grade = "中等"
        elif percentage >= 60:
            grade = "及格"
        else:
            grade = "需要改进"
        
        print(f"完成度: {percentage:.1f}%")
        print(f"等级: {grade}")
        
        # 改进建议
        print("\n改进建议:")
        if self.total_score < 60:
            print("- 重点完成基础功能的实现")
        if any(r['name'] in ['史低价格发现', '交易机会分析', '三角套利计算'] and r['score'] < r['max_score'] for r in self.test_results):
            print("- 重点实现交易分析功能，这是本程序的核心价值")
        if any(r['name'].startswith('错误处理') and r['score'] < r['max_score'] for r in self.test_results):
            print("- 加强错误处理机制")
        if any(r['name'] == '挑战功能' and r['score'] == 0 for r in self.test_results):
            print("- 尝试实现一些挑战功能来获得额外分数")

        print("\n特别提示:")
        print("- 史低价格发现：帮助学生找到日元和人民币的3年史低、5年史低")
        print("- 交易机会分析：找到30天最佳买入卖出点，计算盈利")
        print("- 三角套利：比较 CNY->JPY->USD 和 CNY->USD 哪个更划算")
    
    def run_all_tests(self):
        """运行所有测试"""
        if not self.load_student_code():
            return

        self.test_class_structure()
        self.test_initialization()
        self.test_current_rate_method()
        self.test_historical_data_method()
        self.test_chart_creation()
        self.test_trading_analysis_methods()  # 新增的交易分析测试
        self.test_error_handling()
        self.test_bonus_features()

        self.generate_report()

def main():
    grader = ForexTradingGrader()
    grader.run_all_tests()

if __name__ == "__main__":
    main()
