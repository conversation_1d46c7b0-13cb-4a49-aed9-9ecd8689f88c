"""
外汇交易机会分析程序 - 高级示例实现
这是一个展示如何实现交易分析功能的示例代码

主要功能：
1. 史低价格发现 (3年、5年史低)
2. 30天交易机会分析
3. 三角套利计算 (CNY->JPY->USD vs CNY->USD)
4. 策略比较和推荐

注意：这个示例使用模拟数据，实际实现需要调用真实的汇率API
"""

import requests
import json
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import random
import csv

class ForexTradingAnalyzer:
    def __init__(self, base_currency="CNY", target_currency="JPY"):
        self.base_currency = base_currency
        self.target_currency = target_currency
        self.api_base_url = "https://api.exchangerate-api.com/v4/latest/"
        self.data_cache = {}
        self.supported_currencies = ["CNY", "JPY", "USD", "EUR", "GBP"]
        
    def get_current_rate(self):
        """获取当前汇率 - 示例实现"""
        try:
            # 实际实现应该调用真实API
            # 这里使用模拟数据
            if self.base_currency == "CNY" and self.target_currency == "JPY":
                return round(random.uniform(20.0, 22.0), 4)
            elif self.base_currency == "CNY" and self.target_currency == "USD":
                return round(random.uniform(0.13, 0.15), 4)
            elif self.base_currency == "JPY" and self.target_currency == "USD":
                return round(random.uniform(0.0065, 0.0075), 4)
            else:
                return round(random.uniform(0.1, 2.0), 4)
        except Exception as e:
            print(f"获取当前汇率失败: {e}")
            return None
    
    def get_historical_rates(self, days=30):
        """获取历史汇率数据 - 示例实现"""
        try:
            dates = []
            rates = []
            
            # 生成模拟历史数据
            base_rate = 21.0 if self.target_currency == "JPY" else 0.14
            
            for i in range(days):
                date = datetime.now() - timedelta(days=days-i)
                dates.append(date.strftime("%Y-%m-%d"))
                
                # 添加随机波动
                variation = random.uniform(-0.05, 0.05)
                rate = base_rate * (1 + variation)
                rates.append(round(rate, 4))
            
            return dates, rates
        except Exception as e:
            print(f"获取历史数据失败: {e}")
            return None, None
    
    def find_historical_lows(self, currency_pair="CNY/JPY"):
        """找到历史最低价格 - 核心交易分析功能"""
        try:
            print(f"\n🔍 正在分析 {currency_pair} 的历史最低价格...")
            
            # 获取3年数据 (1095天)
            dates_3y, rates_3y = self.get_historical_rates(1095)
            # 获取5年数据 (1825天) 
            dates_5y, rates_5y = self.get_historical_rates(1825)
            
            if not rates_3y or not rates_5y:
                return None
            
            # 找到史低
            min_3y_idx = rates_3y.index(min(rates_3y))
            min_5y_idx = rates_5y.index(min(rates_5y))
            
            current_rate = self.get_current_rate()
            
            result = {
                "3_year_low": {
                    "date": dates_3y[min_3y_idx],
                    "rate": rates_3y[min_3y_idx],
                    "days_ago": 1095 - min_3y_idx
                },
                "5_year_low": {
                    "date": dates_5y[min_5y_idx], 
                    "rate": rates_5y[min_5y_idx],
                    "days_ago": 1825 - min_5y_idx
                },
                "current_rate": current_rate
            }
            
            # 计算机会分析
            if current_rate:
                diff_3y = ((current_rate - result["3_year_low"]["rate"]) / result["3_year_low"]["rate"]) * 100
                diff_5y = ((current_rate - result["5_year_low"]["rate"]) / result["5_year_low"]["rate"]) * 100
                
                result["opportunity"] = f"当前价格比3年史低高{diff_3y:.1f}%，比5年史低高{diff_5y:.1f}%"
                
                if diff_3y < 5:
                    result["recommendation"] = "🟢 接近3年史低，可能是买入机会！"
                elif diff_5y < 10:
                    result["recommendation"] = "🟡 价格相对较低，可以考虑买入"
                else:
                    result["recommendation"] = "🔴 价格较高，建议等待更好时机"
            
            return result
            
        except Exception as e:
            print(f"史低分析失败: {e}")
            return None
    
    def find_trading_opportunities(self, days=30):
        """找到30天交易机会 - 买入卖出点分析"""
        try:
            print(f"\n📈 正在分析过去{days}天的最佳交易机会...")
            
            dates, rates = self.get_historical_rates(days)
            if not rates:
                return None
            
            # 找到最佳买入点和卖出点
            min_rate = min(rates)
            max_rate = max(rates)
            min_idx = rates.index(min_rate)
            max_idx = rates.index(max_rate)
            
            # 计算收益率
            profit_rate = ((max_rate - min_rate) / min_rate) * 100
            
            result = {
                "buy_point": {
                    "date": dates[min_idx],
                    "rate": min_rate
                },
                "sell_point": {
                    "date": dates[max_idx], 
                    "rate": max_rate
                },
                "profit_rate": round(profit_rate, 2),
                "profit_1000": round(1000 * profit_rate / 100, 2),
                "profit_10000": round(10000 * profit_rate / 100, 2)
            }
            
            # 添加策略建议
            if profit_rate > 10:
                result["strategy"] = "🟢 高收益机会！如果能预测到这些点位，收益很可观"
            elif profit_rate > 5:
                result["strategy"] = "🟡 中等收益机会，需要精准把握时机"
            else:
                result["strategy"] = "🔴 收益较低，可能不值得频繁交易"
            
            return result
            
        except Exception as e:
            print(f"交易机会分析失败: {e}")
            return None
    
    def calculate_arbitrage(self, amount=10000):
        """计算三角套利机会 - 最有趣的功能！"""
        try:
            print(f"\n💰 正在计算三角套利机会 (投资金额: {amount}元人民币)...")
            
            # 获取汇率 (模拟数据)
            cny_to_usd = 0.14  # 人民币直接换美元
            cny_to_jpy = 21.0  # 人民币换日元
            jpy_to_usd = 0.0069  # 日元换美元
            
            # 路径1: 直接换汇 CNY -> USD
            direct_usd = amount * cny_to_usd
            
            # 路径2: 三角套利 CNY -> JPY -> USD
            jpy_amount = amount * cny_to_jpy
            arbitrage_usd = jpy_amount * jpy_to_usd
            
            # 计算套利收益
            arbitrage_profit = arbitrage_usd - direct_usd
            profit_percentage = (arbitrage_profit / direct_usd) * 100
            
            result = {
                "direct_path": {
                    "route": "CNY -> USD",
                    "cny_to_usd_rate": cny_to_usd,
                    "final_usd": round(direct_usd, 2)
                },
                "arbitrage_path": {
                    "route": "CNY -> JPY -> USD",
                    "cny_to_jpy_rate": cny_to_jpy,
                    "jpy_to_usd_rate": jpy_to_usd,
                    "final_usd": round(arbitrage_usd, 2)
                },
                "arbitrage_profit": round(arbitrage_profit, 2),
                "profit_percentage": round(profit_percentage, 3)
            }
            
            # 添加策略建议
            if arbitrage_profit > 0:
                result["recommendation"] = f"🟢 套利机会！通过三角套利可以多赚{arbitrage_profit:.2f}美元"
            elif arbitrage_profit < -10:
                result["recommendation"] = f"🔴 直接换汇更划算，能多得{abs(arbitrage_profit):.2f}美元"
            else:
                result["recommendation"] = "🟡 两种方式差不多，选择更方便的即可"
            
            return result
            
        except Exception as e:
            print(f"套利计算失败: {e}")
            return None
    
    def display_results(self):
        """展示所有分析结果"""
        print("\n" + "="*70)
        print("🏦 外汇交易机会分析报告")
        print("="*70)
        
        # 1. 史低分析
        lows = self.find_historical_lows()
        if lows:
            print(f"\n📊 史低价格分析:")
            print(f"   3年史低: {lows['3_year_low']['rate']} ({lows['3_year_low']['date']})")
            print(f"   5年史低: {lows['5_year_low']['rate']} ({lows['5_year_low']['date']})")
            print(f"   当前汇率: {lows['current_rate']}")
            print(f"   机会分析: {lows['opportunity']}")
            print(f"   投资建议: {lows.get('recommendation', '无建议')}")
        
        # 2. 交易机会分析
        opportunities = self.find_trading_opportunities()
        if opportunities:
            print(f"\n📈 30天交易机会分析:")
            print(f"   最佳买入: {opportunities['buy_point']['rate']} ({opportunities['buy_point']['date']})")
            print(f"   最佳卖出: {opportunities['sell_point']['rate']} ({opportunities['sell_point']['date']})")
            print(f"   收益率: {opportunities['profit_rate']}%")
            print(f"   投资1000元可赚: {opportunities['profit_1000']}元")
            print(f"   投资10000元可赚: {opportunities['profit_10000']}元")
            print(f"   策略建议: {opportunities['strategy']}")
        
        # 3. 三角套利分析
        arbitrage = self.calculate_arbitrage()
        if arbitrage:
            print(f"\n💰 三角套利分析:")
            print(f"   直接路径: {arbitrage['direct_path']['route']} = {arbitrage['direct_path']['final_usd']}美元")
            print(f"   套利路径: {arbitrage['arbitrage_path']['route']} = {arbitrage['arbitrage_path']['final_usd']}美元")
            print(f"   套利收益: {arbitrage['arbitrage_profit']}美元 ({arbitrage['profit_percentage']}%)")
            print(f"   投资建议: {arbitrage['recommendation']}")
        
        print("\n" + "="*70)
        print("分析完成！以上数据基于模拟数据，实际交易请使用真实汇率API")
        print("="*70)
    
    def run(self):
        """主运行方法"""
        print("🌟 欢迎使用外汇交易机会分析程序！")
        print("本程序将帮助你发现:")
        print("- 日元和人民币的历史最低价格")
        print("- 30天最佳买入卖出时机") 
        print("- 三角套利机会分析")
        print("\n开始分析...")
        
        self.display_results()

def main():
    analyzer = ForexTradingAnalyzer()
    analyzer.run()

if __name__ == "__main__":
    main()
