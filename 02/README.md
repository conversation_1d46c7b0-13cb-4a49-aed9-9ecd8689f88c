# L2 - 汇率API与数据可视化

## 教学目标

本项目通过汇率API学习Python网络编程和数据可视化基础：

- **requests.get()** - 发送HTTP请求获取汇率数据
- **JSON数据解析** - 从API响应中提取目标货币汇率
- **matplotlib基础** - plt.plot(), plt.title(), plt.xlabel(), plt.ylabel(), plt.grid(), plt.show()
- **数据处理** - 历史数据获取和趋势分析

## 文件说明

### 1. 分层次练习模板 (三个难度级别)

#### `practice_easy.py` - 简单级别
- **适合对象**: 完全初学者，第一次接触API和绘图
- **练习方式**: 填空练习，重点学习JSON解析和matplotlib基础函数
- **学习重点**: 从汇率API的JSON响应中找到目标货币汇率，掌握基础绘图
- **完成时间**: 15-20分钟

#### `practice_medium.py` - 中等级别
- **适合对象**: 有Python基础，了解基本语法
- **练习方式**: 半引导式，给出函数框架和要求
- **学习重点**: 独立实现API调用、数据解析和图表创建
- **完成时间**: 25-35分钟

#### `practice_hard.py` - 困难级别 ⭐ **外汇交易机会分析版**
- **适合对象**: 有编程经验，想要挑战自己
- **练习方式**: 只给出类框架，需要完全自主实现
- **学习重点**: 面向对象编程，**外汇交易机会分析系统**
- **完成时间**: 60-90分钟

**🚀 新增高级交易分析功能：**
1. **史低价格发现**: 找到日元和人民币的3年史低、5年史低
2. **30天交易机会**: 分析最佳买入卖出点，计算盈利
3. **三角套利**: 比较 CNY→JPY→USD vs CNY→USD 哪个更划算
4. **策略比较**: 比较不同策略的收益率和风险

### 2. `practice_answers.py` - 练习答案
- 包含所有难度级别的完整答案
- 供教师参考和学生对照

### 2.5. `practice_hard_advanced_example.py` - 高级示例 🌟
- **外汇交易分析功能的完整示例实现**
- 展示如何实现史低发现、交易机会分析、三角套利
- 包含详细的结果展示和策略建议
- 使用模拟数据，便于理解算法逻辑

### 3. `auto_grader.py` - 自动评分 (已升级支持交易分析)
- **适用对象**: 困难级别练习 (`practice_hard.py`)
- **评分内容**: 8个维度，总分100分
- **新增评分项目**:
  - 史低价格发现功能 (10分)
  - 交易机会分析功能 (10分)
  - 三角套利计算功能 (10分)
- **功能特点**:
  - 自动测试API调用功能
  - 检查数据解析和图表创建
  - **测试交易分析方法的实现**
  - 详细的评分报告和改进建议
- **使用方法**: `python auto_grader.py`

## API说明

### 当前汇率API
```
https://api.frankfurter.app/latest?from=JPY&to=CNY
```

### 历史汇率API
```
https://api.frankfurter.app/2025-06-19..2025-06-25?from=JPY&to=CNY
```

## 重点学习内容

### JSON数据解析
学习如何从API返回的JSON中提取目标货币汇率：
```python
data = json.loads(response.text)
cny_rate = data['rates']['CNY']
```

### Matplotlib基础绘图
掌握基础的图表创建函数：
```python
plt.plot(dates, rates)      # 创建线图
plt.title("标题")           # 设置标题
plt.xlabel("X轴标签")       # 设置X轴标签
plt.ylabel("Y轴标签")       # 设置Y轴标签
plt.grid()                  # 显示网格
plt.show()                  # 显示图表
```

## 使用建议

1. **循序渐进**: 从简单级别开始，逐步提高难度
2. **重点理解**: 专注于JSON数据结构和matplotlib基础函数
3. **实践为主**: 多动手练习，理解API调用和数据可视化流程
4. **错误处理**: 在中等和困难级别中注意网络请求的错误处理

## 🚀 外汇交易机会分析详解 (困难级别新功能)

### 1. 史低价格发现 📉
**功能目标**: 帮助学生找到日元和人民币的历史最低价格
```python
def find_historical_lows(self, currency_pair="CNY/JPY"):
    # 获取3年数据 (1095天)
    # 获取5年数据 (1825天)
    # 找到史低价格和日期
    # 分析当前价格与史低的差距
    # 给出投资建议
```

**学习价值**:
- 理解长期历史数据的重要性
- 学习寻找投资机会的方法
- 掌握数据分析和比较技巧

### 2. 30天交易机会分析 📈
**功能目标**: 找到最佳买入卖出时机，计算盈利
```python
def find_trading_opportunities(self, days=30):
    # 分析30天汇率波动
    # 找到最低点 (买入时机)
    # 找到最高点 (卖出时机)
    # 计算收益率和具体盈利金额
```

**实际应用**:
- 投资1000元能赚多少？
- 投资10000元能赚多少？
- 不同投资金额的风险评估

### 3. 三角套利机会 💰
**功能目标**: 发现汇率差价机会，比较两种换汇路径
```python
def calculate_arbitrage(self, amount=10000):
    # 路径1: CNY → USD (直接)
    # 路径2: CNY → JPY → USD (套利)
    # 计算哪种方式得到更多美元
    # 分析套利收益和风险
```

**数学原理**:
- 直接路径: 10000 × 0.14 = 1400 USD
- 套利路径: 10000 × 21.0 × 0.0069 = 1449 USD
- 套利收益: 1449 - 1400 = 49 USD (3.5%收益)

### 4. 策略比较和推荐 🎯
**功能目标**: 比较所有策略，给出最佳建议
- 史低买入策略的风险和收益
- 短期交易策略的可行性
- 套利策略的实操难度
- 综合推荐和风险提示

## 💡 教学价值

### 编程技能提升:
- **面向对象编程**: 复杂类的设计和实现
- **数据处理**: 大量历史数据的分析和计算
- **算法思维**: 寻找最优解的逻辑设计
- **错误处理**: 网络请求和数据异常的处理

### 金融知识学习:
- **汇率波动**: 理解外汇市场的基本规律
- **投资策略**: 学习不同投资方法的优缺点
- **风险管理**: 了解投资风险评估方法
- **套利原理**: 掌握金融套利的基本概念

### 实际应用能力:
- **数据分析**: 从大量数据中发现有价值的信息
- **决策支持**: 基于数据分析做出投资决策
- **工具开发**: 开发实用的金融分析工具

## 依赖库

```bash
pip install requests matplotlib
```

## 🌟 开始你的外汇交易分析之旅！

这不仅仅是一个编程练习，更是一个实用的金融分析工具。通过完成这个项目，学生将：

1. **掌握高级编程技能** - 面向对象、数据处理、算法设计
2. **学习金融分析方法** - 历史数据分析、交易策略、风险评估
3. **开发实用工具** - 可以真正用于外汇投资决策的程序
4. **培养数据思维** - 从数据中发现机会和规律的能力

**立即开始挑战 `practice_hard.py`，成为外汇交易分析专家！** 🚀
