"""
练习模板 - 困难级别 (外汇交易机会分析版)

项目要求：
创建一个完整的外汇交易机会分析程序，实现以下功能：

核心功能：
1. 获取实时汇率数据
2. 获取历史汇率数据 (支持3年、5年历史)
3. 数据解析和处理
4. 创建汇率趋势图表
5. 汇率分析和统计

高级交易分析功能 (挑战)：
6. 史低价格发现 (3年史低、5年史低)
7. 交易机会分析 (30天买入卖出点)
8. 套利机会发现 (三角套利: CNY->JPY->USD vs CNY->USD)
9. 盈利计算和比较
10. 交易策略推荐

扩展功能：
11. 支持多种货币对比
12. 数据保存到文件
13. 汇率预警功能
14. 交互式用户界面

技术要求：
- 使用面向对象编程 (创建类)
- 实现完整的错误处理
- 代码结构清晰
- 用户界面友好
- 数学计算准确

评分标准：
- 基础功能实现 (40分)
- 交易分析功能 (30分)
- 错误处理完善 (15分)
- 代码结构清晰 (10分)
- 挑战功能实现 (5分)
"""

import requests
import json
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import csv

class ForexTradingAnalyzer:
    """
    外汇交易机会分析器类

    你需要实现以下方法：

    基础方法：
    - __init__: 初始化方法
    - get_current_rate: 获取当前汇率
    - get_historical_rates: 获取历史汇率
    - parse_data: 解析数据
    - create_chart: 创建图表
    - analyze_rates: 分析汇率

    交易分析方法：
    - find_historical_lows: 找到史低价格 (3年、5年)
    - find_trading_opportunities: 找到30天交易机会
    - calculate_arbitrage: 计算三角套利机会
    - compare_profits: 比较不同策略的盈利
    - recommend_strategy: 推荐交易策略

    扩展方法 (挑战)：
    - save_data: 保存数据
    - set_alert: 设置汇率预警
    - display_trading_menu: 显示交易菜单
    - run: 主运行方法
    """
    
    def __init__(self, base_currency="CNY", target_currency="JPY"):
        """
        初始化外汇交易分析器

        TODO: 设置必要的属性
        - 基础货币和目标货币 (默认人民币到日元)
        - API基础URL (使用免费汇率API)
        - 数据存储字典
        - 交易分析参数
        - 支持的货币列表: CNY, JPY, USD, EUR等
        """
        pass
    
    def get_current_rate(self):
        """
        获取当前汇率
        
        TODO: 实现获取当前汇率的逻辑
        返回: 汇率值或None
        """
        pass
    
    def get_historical_rates(self, days=30):
        """
        获取历史汇率数据 (支持长期数据)

        TODO: 实现获取历史数据的逻辑
        参数: days - 获取多少天的数据 (支持30, 365, 1095, 1825天)
              30天 = 1个月, 365天 = 1年, 1095天 = 3年, 1825天 = 5年
        返回: (日期列表, 汇率列表) 或 (None, None)

        提示: 可以使用免费API如 exchangerate-api.com 或 fixer.io
        """
        pass
    
    def parse_data(self, response):
        """
        解析API响应数据
        
        TODO: 实现数据解析逻辑
        参数: response - API响应对象
        返回: 解析后的数据或None
        """
        pass
    
    def create_chart(self, dates, rates, title="Exchange Rate Trend"):
        """
        创建汇率图表
        
        TODO: 实现图表创建逻辑
        参数: 
        - dates: 日期列表
        - rates: 汇率列表
        - title: 图表标题
        """
        pass
    
    def analyze_rates(self, rates):
        """
        分析汇率数据

        TODO: 实现汇率分析逻辑
        - 计算平均值、最大值、最小值
        - 计算变化趋势
        - 显示统计信息
        参数: rates - 汇率列表
        """
        pass

    def find_historical_lows(self, currency_pair="CNY/JPY"):
        """
        找到历史最低价格 (交易分析核心功能)

        TODO: 实现史低价格发现逻辑
        - 获取3年历史数据，找到3年史低
        - 获取5年历史数据，找到5年史低
        - 分析当前价格与史低的差距
        - 判断是否接近史低买入机会

        参数: currency_pair - 货币对 (如 "CNY/JPY")
        返回: {
            "3_year_low": {"date": "2022-03-15", "rate": 0.042, "days_ago": 850},
            "5_year_low": {"date": "2020-03-20", "rate": 0.041, "days_ago": 1580},
            "current_rate": 0.045,
            "opportunity": "当前价格比3年史低高7.1%，比5年史低高9.8%"
        }
        """
        pass

    def find_trading_opportunities(self, days=30):
        """
        找到30天交易机会 (买入卖出点分析)

        TODO: 实现交易机会分析逻辑
        - 获取30天历史数据
        - 找到最佳买入点 (最低价格的日期)
        - 找到最佳卖出点 (最高价格的日期)
        - 计算如果按最佳策略交易能赚多少钱
        - 分析不同投资金额的收益

        参数: days - 分析天数 (默认30天)
        返回: {
            "buy_point": {"date": "2025-06-01", "rate": 0.043},
            "sell_point": {"date": "2025-06-15", "rate": 0.047},
            "profit_rate": 0.093,  # 9.3%收益率
            "profit_1000": 93,     # 投资1000元能赚93元
            "profit_10000": 930    # 投资10000元能赚930元
        }
        """
        pass

    def calculate_arbitrage(self, amount=10000):
        """
        计算三角套利机会 (高级交易策略)

        TODO: 实现三角套利分析逻辑
        这是最有趣的功能！比较两种换汇路径：

        路径1 (直接): CNY -> USD
        路径2 (套利): CNY -> JPY -> USD

        分析哪种方式能得到更多美元

        参数: amount - 投资金额 (人民币)
        返回: {
            "direct_path": {
                "route": "CNY -> USD",
                "cny_to_usd_rate": 0.14,
                "final_usd": 1400
            },
            "arbitrage_path": {
                "route": "CNY -> JPY -> USD",
                "cny_to_jpy_rate": 20.5,
                "jpy_to_usd_rate": 0.0069,
                "final_usd": 1414.5
            },
            "arbitrage_profit": 14.5,  # 套利多赚14.5美元
            "profit_percentage": 1.04  # 套利收益率1.04%
        }
        """
        pass

    def compare_profits(self, strategies_results):
        """
        比较不同交易策略的盈利 (策略对比)

        TODO: 实现策略比较逻辑
        - 比较史低买入策略
        - 比较30天最佳时机策略
        - 比较三角套利策略
        - 给出最佳策略推荐

        参数: strategies_results - 各种策略的结果字典
        返回: 排序后的策略列表，按盈利从高到低
        """
        pass

    def recommend_strategy(self):
        """
        推荐交易策略 (智能推荐)

        TODO: 实现策略推荐逻辑
        - 分析当前市场情况
        - 结合历史数据
        - 给出具体的交易建议
        - 包括风险提示

        返回: 推荐策略的详细说明
        """
        pass

    def save_data(self, dates, rates, filename="exchange_rates.csv"):
        """
        保存数据到文件 (挑战功能)
        
        TODO: 将汇率数据保存到CSV文件
        参数:
        - dates: 日期列表
        - rates: 汇率列表
        - filename: 文件名
        """
        pass
    
    def set_alert(self, target_rate, alert_type="above"):
        """
        设置汇率预警 (挑战功能)
        
        TODO: 实现汇率预警功能
        参数:
        - target_rate: 目标汇率
        - alert_type: 预警类型 ("above" 或 "below")
        """
        pass
    
    def display_trading_menu(self):
        """
        显示交易分析菜单 (挑战功能)

        TODO: 实现交互式交易菜单
        菜单选项应该包括：
        1. 查看当前汇率
        2. 查看历史汇率图表
        3. 寻找史低价格 (3年/5年)
        4. 分析30天交易机会
        5. 计算三角套利机会
        6. 比较所有策略收益
        7. 获取交易策略推荐
        8. 保存分析数据
        9. 退出程序
        """
        pass
    
    def run(self):
        """
        主运行方法 - 外汇交易机会分析程序

        TODO: 实现主程序逻辑
        - 显示欢迎信息和程序介绍
        - 显示交易分析菜单
        - 处理用户选择
        - 调用相应的分析方法
        - 展示分析结果
        - 提供循环交互直到用户退出

        程序流程建议：
        1. 欢迎信息
        2. 循环显示菜单
        3. 根据用户选择执行对应功能
        4. 显示结果
        5. 询问是否继续
        """
        pass

def main():
    """
    程序入口点 - 外汇交易机会分析程序
    """
    # TODO: 创建ForexTradingAnalyzer实例并运行
    # 示例代码结构：
    # analyzer = ForexTradingAnalyzer()
    # analyzer.run()

    print("欢迎使用外汇交易机会分析程序！")
    print("这个程序可以帮助你：")
    print("- 找到日元和人民币的3年史低、5年史低")
    print("- 分析30天最佳买入卖出时机")
    print("- 发现三角套利机会 (CNY->JPY->USD vs CNY->USD)")
    print("- 比较不同策略的收益")
    print("\n请完成ForexTradingAnalyzer类的实现来开始分析！")
    pass

if __name__ == "__main__":
    main()
